groups:
  - name: service_alerts
    rules:
      # Service Down Alerts
      - alert: ServiceDown
        expr: up == 0
        for: 30s
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.job }} is down"
          description: "Service {{ $labels.job }} on {{ $labels.instance }} has been down for more than 30 seconds."

      # High Response Time
      - alert: HighResponseTime
        expr: http_request_duration_seconds{quantile="0.95"} > 2
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High response time on {{ $labels.job }}"
          description: "95th percentile response time is {{ $value }}s on {{ $labels.job }}"

      # High Error Rate
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "High error rate on {{ $labels.job }}"
          description: "Error rate is {{ $value }} errors per second on {{ $labels.job }}"

      # High Memory Usage
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is above 80% for more than 5 minutes"

      # High CPU Usage
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is above 80% for more than 5 minutes"

      # Disk Space Low
      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) * 100 < 10
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Disk space low"
          description: "Disk space is below 10% on {{ $labels.instance }}"

  - name: application_alerts
    rules:
      # API Gateway Specific
      - alert: APIGatewayHighLatency
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="api-gateway"}[5m])) > 1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "API Gateway high latency"
          description: "API Gateway 95th percentile latency is {{ $value }}s"

      # Database Connection Issues
      - alert: DatabaseConnectionError
        expr: increase(database_connection_errors_total[5m]) > 5
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Database connection errors"
          description: "{{ $value }} database connection errors in the last 5 minutes"

      # Redis Connection Issues
      - alert: RedisConnectionError
        expr: increase(redis_connection_errors_total[5m]) > 3
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Redis connection errors"
          description: "{{ $value }} Redis connection errors in the last 5 minutes"

      # Chat Service WebSocket Connections
      - alert: ChatServiceHighConnections
        expr: websocket_connections_active > 1000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High WebSocket connections on Chat Service"
          description: "Chat Service has {{ $value }} active WebSocket connections"
