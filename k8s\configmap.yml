apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: anti-fraud-platform
data:
  NODE_ENV: "production"
  AUTH_SERVICE_URL: "http://auth-service:3001"
  LINK_SERVICE_URL: "http://link-service:3002"
  COMMUNITY_SERVICE_URL: "http://community-service:3003"
  CHAT_SERVICE_URL: "http://chat-service:3004"
  NEWS_SERVICE_URL: "http://news-service:3005"
  ADMIN_SERVICE_URL: "http://admin-service:3006"
  REDIS_URL: "redis://redis:6379"
  
---
apiVersion: v1
kind: Secret
metadata:
  name: app-secrets
  namespace: anti-fraud-platform
type: Opaque
data:
  # Base64 encoded secrets
  # Use: echo -n "your-secret" | base64
  FIREBASE_PROJECT_ID: ""
  FIREBASE_PRIVATE_KEY: ""
  FIREBASE_CLIENT_EMAIL: ""
  JWT_SECRET: ""
  GEMINI_API_KEY: ""
  VIRUSTOTAL_API_KEY: ""
  SCAMADVISER_API_KEY: ""
  SCREENSHOTLAYER_API_KEY: ""
  NEWSAPI_API_KEY: ""
  NEWSDATA_API_KEY: ""
