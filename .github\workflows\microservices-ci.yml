name: Microservices CI/CD

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'services/**'
      - 'client/**'
      - 'docker-compose.microservices.yml'
      - '.github/workflows/microservices-ci.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'services/**'
      - 'client/**'

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Detect changed services
  detect-changes:
    runs-on: ubuntu-latest
    outputs:
      api-gateway: ${{ steps.changes.outputs.api-gateway }}
      auth-service: ${{ steps.changes.outputs.auth-service }}
      link-service: ${{ steps.changes.outputs.link-service }}
      community-service: ${{ steps.changes.outputs.community-service }}
      chat-service: ${{ steps.changes.outputs.chat-service }}
      news-service: ${{ steps.changes.outputs.news-service }}
      admin-service: ${{ steps.changes.outputs.admin-service }}
      frontend: ${{ steps.changes.outputs.frontend }}
    steps:
      - uses: actions/checkout@v4
      - uses: dorny/paths-filter@v2
        id: changes
        with:
          filters: |
            api-gateway:
              - 'services/api-gateway/**'
            auth-service:
              - 'services/auth-service/**'
            link-service:
              - 'services/link-service/**'
            community-service:
              - 'services/community-service/**'
            chat-service:
              - 'services/chat-service/**'
            news-service:
              - 'services/news-service/**'
            admin-service:
              - 'services/admin-service/**'
            frontend:
              - 'client/**'

  # Test and build services
  test-and-build:
    needs: detect-changes
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service: [api-gateway, auth-service, link-service, community-service, chat-service, news-service, admin-service]
    steps:
      - name: Checkout code
        if: needs.detect-changes.outputs[matrix.service] == 'true'
        uses: actions/checkout@v4

      - name: Setup Node.js
        if: needs.detect-changes.outputs[matrix.service] == 'true'
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: services/${{ matrix.service }}/package-lock.json

      - name: Install dependencies
        if: needs.detect-changes.outputs[matrix.service] == 'true'
        run: |
          cd services/${{ matrix.service }}
          npm ci

      - name: Run tests
        if: needs.detect-changes.outputs[matrix.service] == 'true'
        run: |
          cd services/${{ matrix.service }}
          npm test

      - name: Run linting
        if: needs.detect-changes.outputs[matrix.service] == 'true'
        run: |
          cd services/${{ matrix.service }}
          npm run lint || echo "No lint script found"

      - name: Build service
        if: needs.detect-changes.outputs[matrix.service] == 'true'
        run: |
          cd services/${{ matrix.service }}
          npm run build || echo "No build script found"

  # Test and build frontend
  test-frontend:
    needs: detect-changes
    if: needs.detect-changes.outputs.frontend == 'true'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: client/package-lock.json

      - name: Install dependencies
        run: |
          cd client
          npm ci

      - name: Run tests
        run: |
          cd client
          npm test -- --coverage --watchAll=false

      - name: Build frontend
        run: |
          cd client
          npm run build

  # Build and push Docker images
  build-images:
    needs: [detect-changes, test-and-build]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service: [api-gateway, auth-service, link-service, community-service, chat-service, news-service, admin-service]
    steps:
      - name: Checkout code
        if: needs.detect-changes.outputs[matrix.service] == 'true'
        uses: actions/checkout@v4

      - name: Log in to Container Registry
        if: needs.detect-changes.outputs[matrix.service] == 'true'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        if: needs.detect-changes.outputs[matrix.service] == 'true'
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${{ matrix.service }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        if: needs.detect-changes.outputs[matrix.service] == 'true'
        uses: docker/build-push-action@v5
        with:
          context: services/${{ matrix.service }}
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}

  # Build and push frontend image
  build-frontend-image:
    needs: [detect-changes, test-frontend]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main' && needs.detect-changes.outputs.frontend == 'true'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/frontend
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: client
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}

  # Integration tests
  integration-tests:
    needs: [test-and-build, test-frontend]
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Create .env file
        run: |
          echo "FIREBASE_PROJECT_ID=test-project" >> .env
          echo "JWT_SECRET=test-secret" >> .env
          echo "GEMINI_API_KEY=test-key" >> .env

      - name: Start services
        run: |
          docker-compose -f docker-compose.microservices.yml up -d --build
          sleep 30

      - name: Run integration tests
        run: |
          # Add integration test commands here
          echo "Running integration tests..."
          # Example: newman run postman-collection.json

      - name: Stop services
        if: always()
        run: |
          docker-compose -f docker-compose.microservices.yml down

  # Deploy to staging
  deploy-staging:
    needs: [build-images, build-frontend-image]
    if: github.event_name == 'push' && github.ref == 'refs/heads/develop'
    runs-on: ubuntu-latest
    environment: staging
    steps:
      - name: Deploy to staging
        run: |
          echo "Deploying to staging environment..."
          # Add staging deployment commands here

  # Deploy to production
  deploy-production:
    needs: [build-images, build-frontend-image]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    environment: production
    steps:
      - name: Deploy to production
        run: |
          echo "Deploying to production environment..."
          # Add production deployment commands here
