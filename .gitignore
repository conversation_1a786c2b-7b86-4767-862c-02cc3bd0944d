# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.production
.env.render

# Build outputs
build/
dist/
*.tgz
*.tar.gz

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Firebase
.firebase/
firebase-debug.log
firestore-debug.log

# Docker
.dockerignore

# Temporary files
tmp/
temp/

# Documentation and test files (generated)
*_GUIDE.md
*_FIX.md
*_SETUP.md
*_DEPLOYMENT*.md
*_INTEGRATION.md
*_SOLUTION*.md
test-*.js
test-*.html
deploy-*.sh
!scripts/deploy-microservices.sh
!scripts/deploy-firebase-rules.js
deploy-*.bat
setup-*.sh
setup-*.bat
*-debug.js
*-test.js
check-*.js

# API keys and sensitive data
*.key
*.pem
service-account-*.json

# Security - Database files that may contain secrets
services/phishtank-service/data/phishtank-database.json
services/criminalip-service/data/*.json
*.database.json
*-database.json

# Build artifacts
*.zip
*.tar
*.rar

# Editor files
*.sublime-*
.atom/

# Local development
.env.backup
.env.example.local

# Old monolith files (deprecated after microservices migration)
server/
functions/
firebase.json
firestore.rules
firestore.indexes.json
firestore.indexes.new.json
restart-all.bat
restart-all.js

# Backup directories
backup-monolith/
backup-monolith-manual/
