{"name": "factcheck-platform", "version": "1.0.0", "description": "FactCheck Anti-Fraud Platform - Microservices Architecture", "private": true, "scripts": {"start": "echo 🚀 Starting Full Stack Application... && npm run start:full", "start:full": "concurrently --kill-others-on-fail \"npm run start:services:core\" \"npm run start:client:delayed\"", "start:services": "concurrently --kill-others-on-fail \"npm run start:auth\" \"npm run start:api-gateway\" \"npm run start:admin\" \"npm run start:chat\" \"npm run start:community\" \"npm run start:link\" \"npm run start:news\" \"npm run start:criminalip\" \"npm run start:phishtank\"", "start:services:core": "concurrently --kill-others-on-fail \"npm run start:auth\" \"npm run start:api-gateway\" \"npm run start:admin\" \"npm run start:chat\" \"npm run start:community\" \"npm run start:link\" \"npm run start:news\"", "start:client": "cd client && npm start", "start:client:delayed": "node scripts/delay.js 20 && npm run start:client", "start:auth": "cd services/auth-service && npm start", "start:api-gateway": "cd services/api-gateway && npm start", "start:admin": "cd services/admin-service && npm start", "start:chat": "cd services/chat-service && npm start", "start:community": "cd services/community-service && npm start", "start:link": "cd services/link-service && npm start", "start:news": "cd services/news-service && npm start", "start:criminalip": "cd services/criminalip-service && npm start", "start:phishtank": "cd services/phishtank-service && npm start", "dev": "echo 🔧 Starting development environment... && npm run dev:full", "dev:full": "concurrently --kill-others-on-fail \"npm run dev:services\" \"npm run dev:client:delayed\"", "dev:services": "concurrently --kill-others-on-fail \"npm run dev:auth\" \"npm run dev:api-gateway\" \"npm run dev:admin\" \"npm run dev:chat\" \"npm run dev:community\" \"npm run dev:link\" \"npm run dev:news\" \"npm run dev:criminalip\" \"npm run dev:phishtank\"", "dev:client": "cd client && npm run start", "dev:client:delayed": "node scripts/delay.js 20 && npm run dev:client", "dev:auth": "cd services/auth-service && npm run dev", "dev:api-gateway": "cd services/api-gateway && npm run dev", "dev:admin": "cd services/admin-service && npm run dev", "dev:chat": "cd services/chat-service && npm run dev", "dev:community": "cd services/community-service && npm run dev", "dev:link": "cd services/link-service && npm run dev", "dev:news": "cd services/news-service && npm run dev", "dev:criminalip": "cd services/criminalip-service && npm run dev", "dev:phishtank": "cd services/phishtank-service && npm run dev", "install:all": "echo 📦 Installing all dependencies... && npm install && cd client && npm install && cd ../services/auth-service && npm install && cd ../api-gateway && npm install && cd ../admin-service && npm install && cd ../chat-service && npm install && cd ../community-service && npm install && cd ../link-service && npm install && cd ../news-service && npm install && cd ../criminalip-service && npm install && cd ../phishtank-service && npm install && cd ../../ && echo ✅ All dependencies installed!", "deploy:local": "./scripts/deploy-local.sh", "deploy:docker": "./scripts/deploy-docker.sh", "deploy:k8s": "./scripts/deploy-k8s.sh", "stop:local": "./scripts/stop-local.sh", "stop:docker": "./scripts/stop-docker.sh", "stop:k8s": "./scripts/stop-k8s.sh", "health": "curl -s http://localhost:8082/services/status || echo 'Services not running'", "logs:docker": "docker-compose logs -f", "logs:k8s": "kubectl logs -f deployment/api-gateway -n anti-fraud-platform", "monitoring:install": "node scripts/install-monitoring.js", "monitoring:start": "node scripts/start-monitoring.js", "monitoring:stop": "node scripts/stop-monitoring.js", "monitoring:status": "node scripts/check-monitoring-status.js", "monitoring:setup-grafana": "node scripts/setup-grafana.js", "fix:ports": "node scripts/fix-port-conflicts.js", "validate:ports": "node scripts/validate-ports.js", "start:fixed": "node scripts/start-all-fixed.js", "stop:all": "npm run stop:local && npm run monitoring:stop", "start:safe": "node scripts/start-all-fixed.js", "kill:all": "node scripts/kill-all-services.js", "kill": "npm run kill:all", "stop": "npm run kill:all", "restart": "npm run kill:all && npm run start:full", "restart:safe": "npm run kill:all && npm run start:safe"}, "dependencies": {"all": "^0.0.0", "concurrently": "^8.2.2", "prom-client": "^15.1.0"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "workspaces": ["client", "services/*"]}