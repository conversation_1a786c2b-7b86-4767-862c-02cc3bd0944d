{"name": "link-service", "version": "1.0.0", "description": "Link Verification Service for Anti-Fraud Platform", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "build": "echo 'No build step required for Node.js service'"}, "dependencies": {"axios": "^1.6.2", "cheerio": "^1.0.0-rc.12", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "firebase-admin": "^11.11.1", "helmet": "^7.1.0", "joi": "^17.11.0", "morgan": "^1.10.0", "prom-client": "^15.1.0", "puppeteer": "^21.6.1", "url-parse": "^1.5.10", "winston": "^3.11.0"}, "devDependencies": {"@types/jest": "^29.5.8", "eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}, "keywords": ["link-verification", "security-scanning", "microservice", "anti-fraud", "url-analysis"], "author": "Anti-Fraud Platform Team", "license": "MIT"}