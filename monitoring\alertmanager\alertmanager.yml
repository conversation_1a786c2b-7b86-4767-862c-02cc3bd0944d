global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'your-email-password'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'
  routes:
    - match:
        severity: critical
      receiver: 'critical-alerts'
    - match:
        severity: warning
      receiver: 'warning-alerts'

receivers:
  - name: 'web.hook'
    webhook_configs:
      - url: 'http://host.docker.internal:5001/webhook'

  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>'
        from: '<EMAIL>'
        smarthost: 'localhost:587'
        auth_username: '<EMAIL>'
        auth_password: 'your-email-password'
        headers:
          Subject: '🚨 CRITICAL ALERT: {{ .GroupLabels.alertname }}'
        html: |
          <h2>Critical Alert</h2>
          {{ range .Alerts }}
          <p><strong>Alert:</strong> {{ .Annotations.summary }}</p>
          <p><strong>Description:</strong> {{ .Annotations.description }}</p>
          <p><strong>Severity:</strong> {{ .Labels.severity }}</p>
          <p><strong>Instance:</strong> {{ .Labels.instance }}</p>
          <p><strong>Job:</strong> {{ .Labels.job }}</p>
          <hr>
          {{ end }}
    webhook_configs:
      - url: 'http://host.docker.internal:5001/webhook/critical'
        send_resolved: true

  - name: 'warning-alerts'
    email_configs:
      - to: '<EMAIL>'
        from: '<EMAIL>'
        smarthost: 'localhost:587'
        auth_username: '<EMAIL>'
        auth_password: 'your-email-password'
        headers:
          Subject: '⚠️ WARNING: {{ .GroupLabels.alertname }}'
        html: |
          <h2>Warning Alert</h2>
          {{ range .Alerts }}
          <p><strong>Alert:</strong> {{ .Annotations.summary }}</p>
          <p><strong>Description:</strong> {{ .Annotations.description }}</p>
          <p><strong>Severity:</strong> {{ .Labels.severity }}</p>
          <p><strong>Instance:</strong> {{ .Labels.instance }}</p>
          <p><strong>Job:</strong> {{ .Labels.job }}</p>
          <hr>
          {{ end }}
    webhook_configs:
      - url: 'http://host.docker.internal:5001/webhook/warning'
        send_resolved: true

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'dev', 'instance']
