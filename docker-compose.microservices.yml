version: '3.8'

services:
  # API Gateway
  api-gateway:
    build:
      context: ./services/api-gateway
      dockerfile: Dockerfile
    ports:
      - "8082:8082"
    environment:
      - NODE_ENV=development
      - AUTH_SERVICE_URL=http://auth-service:3001
      - LINK_SERVICE_URL=http://link-service:3002
      - COMMUNITY_SERVICE_URL=http://community-service:3003
      - CHAT_SERVICE_URL=http://chat-service:3004
      - NEWS_SERVICE_URL=http://news-service:3005
      - ADMIN_SERVICE_URL=http://admin-service:3006
      - REDIS_URL=redis://redis:6379
    volumes:
      - ./shared:/app/shared
    depends_on:
      - redis
      - auth-service
      - link-service
      - community-service
      - chat-service
      - news-service
      - admin-service
    networks:
      - microservices-network

  # User Authentication Service
  auth-service:
    build:
      context: ./services/auth-service
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - FIREBASE_PRIVATE_KEY=${FIREBASE_PRIVATE_KEY}
      - FIREBASE_CLIENT_EMAIL=${FIREBASE_CLIENT_EMAIL}
      - JWT_SECRET=${JWT_SECRET}
    env_file:
      - .env
    volumes:
      - ./shared:/app/shared
    networks:
      - microservices-network

  # Link Verification Service
  link-service:
    build:
      context: ./services/link-service
      dockerfile: Dockerfile
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=development
      - PORT=3002
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - FIREBASE_PRIVATE_KEY=${FIREBASE_PRIVATE_KEY}
      - FIREBASE_CLIENT_EMAIL=${FIREBASE_CLIENT_EMAIL}
      - VIRUSTOTAL_API_KEY=${VIRUSTOTAL_API_KEY}
      - SCAMADVISER_API_KEY=${SCAMADVISER_API_KEY}
      - SCREENSHOTLAYER_API_KEY=${SCREENSHOTLAYER_API_KEY}
      - AUTH_SERVICE_URL=http://auth-service:3001
    env_file:
      - .env
    volumes:
      - ./shared:/app/shared
    depends_on:
      - auth-service
    networks:
      - microservices-network

  # Community Service
  community-service:
    build:
      context: ./services/community-service
      dockerfile: Dockerfile
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=development
      - PORT=3003
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - FIREBASE_PRIVATE_KEY=${FIREBASE_PRIVATE_KEY}
      - FIREBASE_CLIENT_EMAIL=${FIREBASE_CLIENT_EMAIL}
      - AUTH_SERVICE_URL=http://auth-service:3001
      - REDIS_URL=redis://redis:6379
    env_file:
      - .env
    volumes:
      - ./shared:/app/shared
    depends_on:
      - auth-service
      - redis
    networks:
      - microservices-network

  # Chat/AI Service
  chat-service:
    build:
      context: ./services/chat-service
      dockerfile: Dockerfile
    ports:
      - "3004:3004"
    environment:
      - NODE_ENV=development
      - PORT=3004
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - FIREBASE_PRIVATE_KEY=${FIREBASE_PRIVATE_KEY}
      - FIREBASE_CLIENT_EMAIL=${FIREBASE_CLIENT_EMAIL}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - AUTH_SERVICE_URL=http://auth-service:3001
    env_file:
      - .env
    volumes:
      - ./shared:/app/shared
    depends_on:
      - auth-service
    networks:
      - microservices-network

  # News/Content Service
  news-service:
    build:
      context: ./services/news-service
      dockerfile: Dockerfile
    ports:
      - "3005:3005"
    environment:
      - NODE_ENV=development
      - PORT=3005
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - FIREBASE_PRIVATE_KEY=${FIREBASE_PRIVATE_KEY}
      - FIREBASE_CLIENT_EMAIL=${FIREBASE_CLIENT_EMAIL}
      - NEWSAPI_API_KEY=${NEWSAPI_API_KEY}
      - NEWSDATA_API_KEY=${NEWSDATA_API_KEY}
      - AUTH_SERVICE_URL=http://auth-service:3001
    env_file:
      - .env
    volumes:
      - ./shared:/app/shared
    depends_on:
      - auth-service
    networks:
      - microservices-network

  # Admin Service
  admin-service:
    build:
      context: ./services/admin-service
      dockerfile: Dockerfile
    ports:
      - "3006:3006"
    environment:
      - NODE_ENV=development
      - PORT=3006
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - FIREBASE_PRIVATE_KEY=${FIREBASE_PRIVATE_KEY}
      - FIREBASE_CLIENT_EMAIL=${FIREBASE_CLIENT_EMAIL}
      - AUTH_SERVICE_URL=http://auth-service:3001
      - COMMUNITY_SERVICE_URL=http://community-service:3003
      - LINK_SERVICE_URL=http://link-service:3002
    env_file:
      - .env
    volumes:
      - ./shared:/app/shared
    depends_on:
      - auth-service
      - community-service
      - link-service
    networks:
      - microservices-network

  # Redis for caching and message queuing
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis-data:/data
    networks:
      - microservices-network

  # React Frontend
  frontend:
    build:
      context: ./client
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8082
      - REACT_APP_FIREBASE_API_KEY=${REACT_APP_FIREBASE_API_KEY}
      - REACT_APP_FIREBASE_AUTH_DOMAIN=${REACT_APP_FIREBASE_AUTH_DOMAIN}
      - REACT_APP_FIREBASE_PROJECT_ID=${REACT_APP_FIREBASE_PROJECT_ID}
    env_file:
      - .env
    depends_on:
      - api-gateway
    networks:
      - microservices-network

  # Monitoring Stack
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - microservices-network

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3010:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - microservices-network

  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - microservices-network

volumes:
  redis-data:
  prometheus-data:
  grafana-data:

networks:
  microservices-network:
    driver: bridge
