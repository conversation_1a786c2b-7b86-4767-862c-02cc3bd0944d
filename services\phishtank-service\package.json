{"name": "phishtank-service", "version": "1.0.0", "description": "PhishTank Opensource Service for Anti-Fraud Platform", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "update-database": "node src/scripts/updateDatabase.js"}, "keywords": ["phishtank", "security", "phishing", "threat-intelligence"], "author": "Anti-Fraud Platform", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "express-rate-limit": "^7.1.5", "dotenv": "^16.3.1", "axios": "^1.6.2", "fs-extra": "^11.2.0", "path": "^0.12.7", "uuid": "^9.0.1", "node-cron": "^3.0.3"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}