# FactCheck Security System - Environment Variables Template
# Copy this file to .env and fill in your actual API keys

# ==============================================
# SECURITY ANALYSIS API KEYS
# ==============================================

# APWG (Anti-Phishing Working Group)
# Get API key from: https://apwg.org/ecx/
APWG_API_KEY=your-apwg-api-key-here
APWG_API_URL=https://ecx.apwg.org/api

# ScamAdviser 
# Get API key from: https://api.scamadviser.cloud/docs/
SCAMADVISER_API_KEY=your-scamadviser-api-key-here

# Criminal IP
# Contact vendor for API access
CRIMINAL_IP_API_KEY=your-criminal-ip-api-key-here
CRIMINAL_IP_API_URL=https://api.criminalip.io

# Hudson Rock
# Get API key from: https://docs.hudsonrock.com/
HUDSON_ROCK_API_KEY=your-hudson-rock-api-key-here
HUDSON_ROCK_API_URL=https://cavalier.hudsonrock.com/api/json/v2

# PhishTank
# Get API key from: https://phishtank.org/api_info.php
PHISHTANK_API_KEY=your-phishtank-api-key-here

# IP Quality Score
# Get API key from: https://ipqualityscore.com/documentation/overview
IPQUALITYSCORE_API_KEY=your-ipqualityscore-api-key-here

# Google Safe Browsing
# Get API key from: https://developers.google.com/safe-browsing/v4
GOOGLE_SAFEBROWSING_API_KEY=your-google-safebrowsing-api-key-here

# VirusTotal (existing)
VIRUSTOTAL_API_KEY=your-virustotal-api-key-here

# ==============================================
# SERVICES WITHOUT PUBLIC APIs (Future Implementation)
# ==============================================

# Cyradar - No public API
# Tín Nhiệm Mạng - No public API  
# NCSC Vietnam - No public API
# ScamVN - No public API
# BforeAI - Private API

# ==============================================
# AI SERVICES (existing)
# ==============================================

# OpenAI
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=500
OPENAI_TEMPERATURE=0.7

# Google Gemini
GEMINI_API_KEY=your-gemini-api-key-here
GEMINI_MODEL=gemini-1.5-flash

# ==============================================
# OTHER SERVICES (existing)
# ==============================================

# Screenshot Service
SCREENSHOT_API_KEY=your-screenshot-api-key-here

# ==============================================
# DATABASE & FIREBASE (existing)
# ==============================================

# Firebase
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY=your-firebase-private-key
FIREBASE_CLIENT_EMAIL=your-firebase-client-email

# ==============================================
# GENERAL SETTINGS
# ==============================================

# Environment
NODE_ENV=development

# Server
PORT=8082

# Security
JWT_SECRET=your-jwt-secret-here
JWT_EXPIRE=7d

# Frontend URL (for CORS and redirects)
FRONTEND_URL=http://localhost:3000

# Email Configuration (for notifications)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-gmail-app-password

# Rate Limiting
ENABLE_RATE_LIMITING=true
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info

# ==============================================
# API CONFIGURATION
# ==============================================

# Default timeouts (milliseconds)
API_TIMEOUT=30000
SECURITY_ANALYSIS_TIMEOUT=45000

# Rate limiting for external APIs
APWG_RATE_LIMIT_MS=1000
PHISHTANK_RATE_LIMIT_MS=2000
HUDSON_ROCK_RATE_LIMIT_MS=1000
CRIMINAL_IP_RATE_LIMIT_MS=1000
IPQUALITYSCORE_RATE_LIMIT_MS=500

# ==============================================
# MOCK DATA SETTINGS
# ==============================================

# Set to true to use mock data when APIs are not configured
USE_MOCK_DATA_FALLBACK=true

# Mock data randomization
MOCK_DATA_RANDOM_SEED=12345

# ==============================================
# CLIENT CONFIGURATION
# ==============================================

# API URLs for client-side connections
REACT_APP_API_URL=http://localhost:5000/api

# App Information
REACT_APP_APP_NAME=FactCheck
REACT_APP_VERSION=1.0.0

# Client Environment
REACT_APP_NODE_ENV=development
