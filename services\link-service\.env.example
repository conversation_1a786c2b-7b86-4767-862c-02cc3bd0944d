# Link Service Environment Variables

# Server Configuration
PORT=3002
NODE_ENV=development

# Firebase Configuration
FIREBASE_PROJECT_ID=factcheck-platform-dev
FIREBASE_PRIVATE_KEY_ID=your_private_key_id
FIREBASE_PRIVATE_KEY="-----B<PERSON><PERSON> PRIVATE KEY-----\nyour_private_key\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your_client_id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_CLIENT_X509_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-xxxxx%40factcheck-platform-dev.iam.gserviceaccount.com

# Firebase Emulator (for development)
FIREBASE_AUTH_EMULATOR_HOST=localhost:9099
FIRESTORE_EMULATOR_HOST=localhost:8080

# Screenshot API
SCREENSHOTONE_API_KEY=your_screenshotone_api_key
SCREENSHOTONE_SECRET=your_screenshotone_secret

# Security APIs
GOOGLE_SAFE_BROWSING_API_KEY=your_google_safe_browsing_key
PHISHTANK_API_KEY=your_phishtank_api_key
SCAMADVISER_API_KEY=your_scamadviser_api_key
CRIMINALIP_API_KEY=your_criminalip_api_key
IPQUALITYSCORE_API_KEY=your_ipqualityscore_api_key

# VirusTotal API (existing)
VIRUSTOTAL_API_KEY=your_virustotal_api_key

# Hudson Rock API
HUDSONROCK_API_KEY=your_hudsonrock_api_key

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
